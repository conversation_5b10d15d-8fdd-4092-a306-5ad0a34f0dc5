import { useWalletData } from "@/src/hooks/useWalletData";
import { Box, Button, GradientIconButton, Screen, TextInput } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { Text } from "@/src/ui/components/Text";
import { formatCurrency } from "@/src/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Keyboard, Platform } from "react-native";
import { z } from "zod";

// Funções de formatação automática para chaves PIX
// Exemplos de formatação:
// CPF: 06809884165 → 068.098.841-65
// Telefone: 62984033533 → (62) 98403-3533
// CNPJ: 12345678000190 → 12.345.678/0001-90

const formatCPF = (value: string): string => {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length <= 11) {
    return numbers
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d)/, '$1.$2')
      .replace(/(\d{3})(\d{1,2})$/, '$1-$2');
  }
  return value;
};

const formatPhone = (value: string): string => {
  const numbers = value.replace(/\D/g, '');
  if (numbers.length <= 11) {
    if (numbers.length <= 2) {
      return numbers;
    } else if (numbers.length <= 7) {
      return numbers.replace(/(\d{2})(\d)/, '($1) $2');
    } else if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d)/, '($1) $2-$3');
    } else {
      return numbers.replace(/(\d{2})(\d{5})(\d)/, '($1) $2-$3');
    }
  }
  return value;
};

const formatPixKey = (value: string): string => {
  // Se tem @ é email, não formatar
  if (value.includes('@')) {
    return value;
  }

  // Remove toda formatação para análise
  const numbers = value.replace(/\D/g, '');

  // Se não tem números suficientes, retorna como está
  if (numbers.length === 0) {
    return value;
  }

  // Se tem apenas números (sem letras ou símbolos especiais além de formatação)
  if (value.replace(/[\d\s\(\)\.-]/g, '') === '') {
    // 14 dígitos = CNPJ
    if (numbers.length === 14) {
      return numbers.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }
    // 11 dígitos = CPF ou Telefone
    else if (numbers.length === 11) {
      // Se começa com padrão de celular (DDD válido + 9), formatar como telefone
      if (/^[1-9][1-9]9/.test(numbers)) {
        return formatPhone(numbers);
      } else {
        // Caso contrário, formatar como CPF
        return formatCPF(numbers);
      }
    }
    // 10 dígitos = Telefone fixo
    else if (numbers.length === 10) {
      return formatPhone(numbers);
    }
    // Menos de 11 dígitos, tentar formatar como CPF se não parecer telefone
    else if (numbers.length <= 11) {
      // Se começa com padrão de DDD, formatar como telefone
      if (numbers.length >= 2 && /^[1-9][1-9]/.test(numbers)) {
        return formatPhone(numbers);
      } else {
        // Caso contrário, formatar como CPF
        return formatCPF(numbers);
      }
    }
  }

  // Para outros casos (UUID, etc), retorna sem formatação
  return value;
};

// Schema de validação por step
const amountSchema = z.object({
  amount: z.string()
    .min(1, "Valor é obrigatório")
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return !isNaN(numValue) && numValue > 0;
      },
      { message: "Digite um valor válido maior que zero" }
    )
    .refine(
      (val) => {
        const numValue = parseFloat(val.replace(',', '.'));
        return numValue <= 100000; // limite de R$ 100.000
      },
      { message: "Valor excede o limite permitido" }
    ),
});

const recipientSchema = z.object({
  pixKey: z.string()
    .min(1, "Chave PIX é obrigatória")
    .refine(
      (val) => {
        // Remove formatação para validação
        const numbersOnly = val.replace(/\D/g, '');

        // Validações para diferentes tipos de chave PIX
        const email = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val);
        const phone = /^\(\d{2}\)\s\d{4,5}-\d{4}$/.test(val) || (numbersOnly.length >= 10 && numbersOnly.length <= 11);
        const cpf = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(val) || numbersOnly.length === 11;
        const cnpj = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/.test(val) || numbersOnly.length === 14;
        const randomKey = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(val);

        return email || phone || cpf || cnpj || randomKey;
      },
      { message: "Digite uma chave PIX válida" }
    ),
});

type AmountFormData = z.infer<typeof amountSchema>;
type RecipientFormData = z.infer<typeof recipientSchema>;

export default function SendPixScreen() {
  const router = useRouter();
  const { balance } = useWalletData();
  const { step, transferValue, pixKey, isEdit } = useLocalSearchParams<{
    step?: 'amount' | 'recipient';
    transferValue?: string;
    pixKey?: string;
    isEdit?: string;
  }>();


  const IS_EDIT_MODE = isEdit === 'true';
  const currentStep = step || 'amount';
  const isAmountStep = currentStep === 'amount';
  const isRecipientStep = currentStep === 'recipient';

  // Form para step de valor
  const amountForm = useForm<AmountFormData>({
    resolver: zodResolver(amountSchema),
    defaultValues: {
      amount: IS_EDIT_MODE ? transferValue : '0,00',
    },
    mode: 'onChange',
  });

  // Form para step de destinatário
  const recipientForm = useForm<RecipientFormData>({
    resolver: zodResolver(recipientSchema),
    defaultValues: {
      pixKey: IS_EDIT_MODE && pixKey ? pixKey : '',
    },
    mode: 'onChange',
  });

  const handleAmountSubmit = (data: AmountFormData) => {
    const newTransferValue = formatCurrency(Number(data.amount));

    // Se está em modo de edição e já tem uma chave PIX, volta direto para confirmação
    if (IS_EDIT_MODE && pixKey) {
      router.replace({
        pathname: '/(protected)/area-pix/confirmation',
        params: {
          transferValue: newTransferValue,
          pixKey: pixKey,
          pixKeyType: 'EDIT' // Indica que veio de uma edição
        }
      });
    } else {
      // Fluxo normal: vai para o step de destinatário
      router.push({
        pathname: '/(protected)/area-pix/send-pix',
        params: {
          step: 'recipient',
          transferValue: newTransferValue
        }
      });
    }
  };

  const handleRecipientSubmit = (data: RecipientFormData) => {
    router.push({
      pathname: '/(protected)/area-pix/confirmation',
      params: {
        transferValue: transferValue,
        pixKey: data.pixKey
      }
    });
  };

  const handleSubmit = isAmountStep
    ? amountForm.handleSubmit(handleAmountSubmit)
    : recipientForm.handleSubmit(handleRecipientSubmit);

  const isFormValid = isAmountStep
    ? amountForm.formState.isValid
    : recipientForm.formState.isValid;

  // Validação de saldo insuficiente para o step de valor
  const currentAmount = isAmountStep ? Number(amountForm.watch('amount')) : 0;
  const availableBalance = balance?.balance ?? 0;
  // const isInsufficientBalance = isAmountStep && currentAmount > 0 && (currentAmount * 100) > availableBalance;
  const isInsufficientBalance = false

  // O botão deve estar habilitado apenas se o formulário for válido E não tiver saldo insuficiente
  const isButtonEnabled = isFormValid && !isInsufficientBalance;

  return (
    <Screen
      customHeader={
        <HeaderPrimary
          title=" "
          canGoBack
        />}
    >
      <Box height="100%" paddingTop="s16">
        {isAmountStep ? (
          <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27} mt="s14">
            {`Qual é o valor da${'\n'}transferência?`}
          </Text>
        ) : (
          <Box mt="s14">
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              Para qual chave você quer
            </Text>
            <Text fontSize={25} fontWeight={'500'} fontFamily="DMSansMedium" lineHeight={27}>
              transferir <Text fontWeight={'700'} fontSize={22} fontFamily="DMSansBold">{transferValue}</Text>?
            </Text>
          </Box>
        )}

        {isAmountStep && (
          <Box gap="s8" mt="s12">
            <Text fontSize={17} fontFamily="DMSansRegular" color="zinc50">
              Saldo disponível de
            </Text>
            <Text fontFamily="DMSansBold" color="gray900">
              {formatCurrency(balance?.balance ?? 0)}
            </Text>
          </Box>
        )}

        {/* primeira etapa */}
        {isAmountStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"

            >
              <Box flexDirection="row">
                <Box flex={1}>
                  <Controller
                    control={amountForm.control}
                    name="amount"
                    render={({ field: { onChange, value } }) => (
                      <TextInput
                        label=""
                        placeholder="0,00"
                        value={formatCurrency(Number(value.replace(/[^0-9]/g, '')))}
                        onChangeText={(text) => onChange(text.replace(/[^0-9]/g, ''))}
                        keyboardType="numeric"
                        style={{
                          fontSize: 25,
                          fontWeight: '600',
                          fontFamily: 'DMSansSemiBold',
                          color: '#1F1F1F',
                          paddingHorizontal: 0,
                          flex: 1,
                        }}
                        boxProps={{
                          backgroundColor: 'background',
                        }}
                      />
                    )}
                  />
                </Box>
              </Box>
            </Box>

            {/* Erro de validação */}
            {amountForm.formState.errors.amount && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {amountForm.formState.errors.amount.message}
              </Text>
            )}

            {/* Erro de saldo insuficiente */}
            {isInsufficientBalance && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                Saldo insuficiente. Disponível: {formatCurrency(availableBalance)}
              </Text>
            )}
          </Box>
        )}

        {/* Input de chave PIX - segunda etapa */}
        {isRecipientStep && (
          <Box mt="s32">
            <Box
              borderBottomWidth={1}
              borderBottomColor="border"
              flexDirection="row"
            >
              <Box flex={1}>
                <Controller
                  control={recipientForm.control}
                  name="pixKey"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label=""
                      placeholderTextColor="#3D40457F"
                      placeholder="CPF/CNPJ, E-mail ou Celular"
                      value={value}
                      onChangeText={(text) => {
                        const formattedText = formatPixKey(text);
                        onChange(formattedText);
                      }}
                      style={{
                        fontSize: 16,
                        borderWidth: 0,
                        paddingHorizontal: 0,
                        backgroundColor: 'transparent',
                      }}
                      boxProps={{
                        backgroundColor: 'background',
                      }}
                    />
                  )}
                />
              </Box>
            </Box>

            {/* Erro de validação */}
            {recipientForm.formState.errors.pixKey && (
              <Text fontSize={14} mt="s8" style={{ color: '#FF0000' }}>
                {recipientForm.formState.errors.pixKey.message}
              </Text>
            )}
          </Box>
        )}

        {isRecipientStep && (
          <Box alignItems="center" mt="s56">
            <Button
              variant="gradient"
              title="Transferir"
              onPress={recipientForm.handleSubmit(handleRecipientSubmit)}
              boxProps={{
                width: '70%',
                borderRadius: 'rounded',
              }}
            />
          </Box>
        )}

        <Box style={{
          flex: 1,
        }}>

        </Box>
        {!isRecipientStep && (
          <GradientIconButton
            iconName="arrowRight"
            onPress={() => {
              if (isButtonEnabled) {
                Keyboard.dismiss();
                handleSubmit();
              }
            }}
            iconOnly
            disabled={!isButtonEnabled}
            style={{
              position: 'absolute',
              bottom: Platform.OS === 'ios' ? 16 : 65,
              right: 20,
              opacity: isButtonEnabled ? 1 : 0.5,
            }}
          />
        )}
      </Box>
    </Screen>
  );
} 