import { dataTransaction } from "@/mock";
import { Box, CardTransaction, EmptyState, Screen, Text } from "@/src/ui/components";
import { HeaderPrimary } from "@/src/ui/components/HeaderPrimary";
import { useMemo, useState } from "react";
import { Dimensions, FlatList } from "react-native";
import Svg, { Circle, Defs, LinearGradient, Path, Stop } from "react-native-svg";

const screenWidth = Dimensions.get('window').width;

type ChartDataPoint = {
  x: number;
  y: number;
};

const months = [
  { id: 1, label: "Out", value: 1 },
  { id: 2, label: "Nov", value: 2 },
  { id: 3, label: "Dez", value: 3 },
  { id: 4, label: "Jan", value: 4 },
  { id: 5, label: "Fev", value: 5 },
  { id: 6, label: "Mar", value: 6 },
];

export default function TransactionHistoryScreen() {
  const [selectedMonth, setSelectedMonth] = useState(4); // <PERSON> é o mês selecionado

  // Processar dados de transação para o gráfico
  const transactionData = useMemo((): ChartDataPoint[] => {
    if (!dataTransaction || dataTransaction.length === 0) {
      // Dados padrão se não houver transações
      return [
        { x: 1, y: 0 },
        { x: 2, y: 0 },
        { x: 3, y: 0 },
        { x: 4, y: 0 },
        { x: 5, y: 0 },
        { x: 6, y: 0 },
      ];
    }

    // Agrupar transações por mês
    const transactionsByMonth = dataTransaction.reduce((acc, transaction) => {
      const date = new Date(transaction.createdAt);
      const month = date.getMonth() + 1; // getMonth() retorna 0-11, então +1 para 1-12

      if (!acc[month]) {
        acc[month] = { totalAmount: 0, count: 0 };
      }

      acc[month].totalAmount += transaction.amount;
      acc[month].count += 1;

      return acc;
    }, {} as Record<number, { totalAmount: number; count: number }>);

    // Mapear para o formato do gráfico (usando valores totais)
    return months.map((month) => ({
      x: month.value,
      y: transactionsByMonth[month.value]?.totalAmount || 0
    }));
  }, []);

  const selectedDataPoint = transactionData.find((point: ChartDataPoint) => point.x === selectedMonth);
  const transactionCount = selectedDataPoint?.y || 0;

  // Calcular dimensões do gráfico
  const chartWidth = screenWidth; // Usar toda a largura da tela
  const chartHeight = 200;
  const padding = 40;
  const graphWidth = chartWidth - (padding * 2);
  const graphHeight = chartHeight - (padding * 2);

  // Encontrar valores mínimos e máximos
  const validYValues = transactionData.map(d => d.y).filter(y => !isNaN(y) && isFinite(y));
  const minY = validYValues.length > 0 ? Math.min(...validYValues) : 0;
  const maxY = validYValues.length > 0 ? Math.max(...validYValues) : 100; // Valor padrão se não há dados válidos
  const yRange = maxY - minY || 1; // Evita divisão por zero

  // Gerar pontos do gráfico
  const points = transactionData.map((point, index) => {
    const x = padding + (index / (transactionData.length - 1)) * graphWidth;
    // Garantir que não há NaN nos cálculos
    const normalizedY = yRange === 0 ? 0.5 : (point.y - minY) / yRange;
    const y = padding + graphHeight - (normalizedY * graphHeight);
    return { x: isNaN(x) ? padding : x, y: isNaN(y) ? padding + graphHeight / 2 : y, original: point };
  });

  // Gerar path para a linha com curvas suaves
  const generateSmoothPath = (points: {x: number, y: number, original: any}[]) => {
    if (points.length < 2) return '';

    // Validar se o primeiro ponto é válido
    const firstPoint = points[0];
    if (isNaN(firstPoint.x) || isNaN(firstPoint.y)) return '';

    let path = `M ${firstPoint.x} ${firstPoint.y}`;

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];

      // Validar se os pontos são válidos
      if (isNaN(curr.x) || isNaN(curr.y)) continue;

      if (next && !isNaN(next.x) && !isNaN(next.y)) {
        // Curva Bézier mais suave
        const tension = 0.3; // Controla a suavidade da curva
        const cp1x = prev.x + (curr.x - prev.x) * (1 - tension);
        const cp1y = prev.y + (curr.y - prev.y) * (1 - tension);
        const cp2x = curr.x - (next.x - curr.x) * tension;
        const cp2y = curr.y - (next.y - curr.y) * tension;

        // Validar pontos de controle
        if (!isNaN(cp1x) && !isNaN(cp1y) && !isNaN(cp2x) && !isNaN(cp2y)) {
          path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${curr.x} ${curr.y}`;
        } else {
          path += ` L ${curr.x} ${curr.y}`;
        }
      } else {
        // Último ponto - linha direta
        path += ` L ${curr.x} ${curr.y}`;
      }
    }

    return path;
  };

  const linePath = generateSmoothPath(points);
  
  // Gerar path para a área com curvas suaves
  const areaPath = generateSmoothPath(points) + 
    ` L ${points[points.length - 1].x} ${chartHeight} L ${points[0].x} ${chartHeight} Z`;

  // Gerar linhas de grade verticais
  const gridLines = months.map((_, index) => {
    const x = padding + (index / (months.length - 1)) * graphWidth;
    const point = points[index];
    return { x, y1: padding, y2: point.y };
  });

  return (
    <Screen
      scrollable
      customHeader={
        <HeaderPrimary
          title="Histórico de transação"
          canGoBack={true}
        />}
    >
        <Box gap="s24">
          {/* Seção do Gráfico */}
        <Box
          paddingVertical="s16"
          borderRadius="default"
          marginTop="s16"
          style={{ marginLeft: -24, marginRight: -24 }} // Remove o padding do Screen
        >
          {/* Título e valor */}
          <Box alignItems="center" gap="s12" marginBottom="s24" paddingHorizontal="s16">
            <Text variant="text16Medium" color="gray500">Transações</Text>
            <Text variant="text25" color="black">{transactionCount.toLocaleString()}</Text>
          </Box>

          {/* Gráfico */}
          <Box width="100%" height={chartHeight}>
            <Svg width="100%" height={chartHeight} viewBox={`0 0 ${chartWidth} ${chartHeight}`}>
              {/* Definição do gradiente */}
              <Defs>
                <LinearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <Stop offset="0%" stopColor="#cecece" stopOpacity={1} />
                  <Stop offset="100%" stopColor="#F9FAFB" stopOpacity={0.3} />
                </LinearGradient>
                <LinearGradient id="lineGradient" x1="0%" y1="100%" x2="0%" y2="0%">
                  <Stop offset="0%" stopColor="#D1D5DB" />
                  <Stop offset="100%" stopColor="#000000" />
                </LinearGradient>
              </Defs>
              
              {/* Linhas de grade verticais */}
              {gridLines.map((line, index) => (
                <Path
                  key={index}
                  d={`M ${line.x} ${line.y1} L ${line.x} ${line.y2}`}
                  stroke="#969798"
                  strokeWidth={0.5}
                  opacity={0.3}
                />
              ))}
              
              {/* Área preenchida com gradiente */}
              <Path
                d={areaPath}
                fill="url(#areaGradient)"
              />
              
              {/* Linha do gráfico */}
              <Path
                d={linePath}
                stroke="url(#lineGradient)"
                strokeWidth={6}
                strokeLinecap="round"
                fill="none"
              />
              
              {/* Ponto selecionado */}
              {selectedDataPoint && (
                <Circle
                  cx={points[selectedMonth - 1]?.x || 0}
                  cy={points[selectedMonth - 1]?.y || 0}
                  r={8}
                  fill="white"
                  stroke="#000000"
                  strokeWidth={4}
                />
              )}
            </Svg>
          </Box>

          {/* Seleção de meses */}
          <Box
            flexDirection="row"
            justifyContent="space-between"
            marginTop="s16"
            paddingHorizontal="s16"
          >
            {months.map((month) => (
              <Box
                key={month.id}
                paddingHorizontal="s12"
                paddingVertical="s8"
                borderRadius="default"
                backgroundColor={selectedMonth === month.value ? "black" : "transparent"}
                onTouchEnd={() => setSelectedMonth(month.value)}
              >
                <Text
                  variant="text14Medium"
                  color={selectedMonth === month.value ? "white" : "gray500"}
                >
                  {month.label}
                </Text>
              </Box>
            ))}
          </Box>
        </Box>

        <Box flex={1} height="100%" mt="s56" borderRadius="secondary" overflow="hidden" >
          <FlatList
            data={dataTransaction ?? []}
            renderItem={({ item }) => <CardTransaction data={item} />}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ gap: 16 }}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <EmptyState
                icon="moneyRecive"
                title="Nenhuma transação encontrada"
                description="Seu histórico de transações aparecerá aqui quando você realizar operações"
              />
            )}
            style={{
              backgroundColor: 'white',
              padding: 20,
              // flexGrow: 1,
            }}
            ListHeaderComponent={() => (
              <Box
                marginTop="s16"
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text variant="text18Medium">Histórico de saques</Text>
              </Box>
            )}
          />
        </Box>
      </Box>
    </Screen>
  );
}