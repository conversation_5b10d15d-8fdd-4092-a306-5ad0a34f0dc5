import { ItemTransaction, Transaction } from "./src/types/transaction";
import { Withdrawal } from "./src/types/withdrawal";

export const dataWithdrawal: Withdrawal[] = [
  {
      id: "cdb73696-dee1-4e48-a21f-1b700c2faf6a",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-08T17:54:51.145Z",
      processedAt: "2025-07-08T17:55:13.676Z",
      createdAt: "2025-07-01T17:54:47.243Z",
      externalRef: "R14mKX9PR5jkYVoq"
  },
  {
      id: "031c00ff-0f81-4d41-a270-0341ea08fbd8",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T16:10:31.509Z",
      processedAt: "2025-07-01T16:10:44.178Z",
      createdAt: "2025-07-01T16:10:27.884Z",
      externalRef: "5WzOwAWQJ1X19a43"
  },
  {
      id: "116c9149-1866-43be-a0d6-f03affb0d2e6",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "FAILED",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T16:06:09.695Z",
      processedAt: "2025-07-01T16:06:07.743Z",
      createdAt: "2025-07-01T16:06:07.743Z",
      externalRef: ""
  },
  {
      id: "b5a930ac-f277-4379-b12c-d1ab4416f473",
      amount: 1000,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T14:27:41.082Z",
      processedAt: "2025-07-01T14:28:12.308Z",
      createdAt: "2025-07-01T14:27:28.477Z",
      externalRef: "JDQwLAdYO3jlRWoG"
  },
  {
    id: "116c9339-1866-43be-a0d6-f03affb0d2e6",
    amount: 600,
    method: "PIX",
    pixKey: "38211277000136",
    pixKeyType: "CNPJ",
    status: "FAILED",
    withdrawalType: "AUTOMATIC",
    approvedAt: "2025-07-01T14:27:41.082Z",
    processedAt: "2025-07-01T14:28:12.308Z",
    createdAt: "2025-07-01T14:27:28.477Z",
    externalRef: "JDQwLAdYO3jlRWoG"
  }
]



export const dataTransaction: ItemTransaction[] = [
  {
    id: "1",
    title: "Compra de produto",
    amount: 100,
    quantity: 1,
    tangible: true,
    externalRef: "1234567890",
    transactionId: "1234567890",
    createdAt: "2025-07-01T14:27:28.477Z",
    updatedAt: "2025-07-01T14:27:28.477Z"
  },
  {
      id: "031c00ff-0f81-4d41-a270-0341ea08fbd8",
      title: "Compra de produto",
      amount: 100,
      quantity: 1,
      tangible: true,
      externalRef: "1234567890",
      transactionId: "1234567890",
      createdAt: "2025-07-01T14:27:28.477Z",
      updatedAt: "2025-07-01T14:27:28.477Z"
  },
  {
      id: "116c9149-1866-43be-a0d6-f03affb0d2e6",
      title: "Compra de produto",
      amount: 100,
      quantity: 1,
      tangible: true,
      externalRef: "1234567890",
      transactionId: "1234567890",
      createdAt: "2025-07-01T14:27:28.477Z",
      updatedAt: "2025-07-01T14:27:28.477Z"
  },
  {
      id: "b5a930ac-f277-4379-b12c-d1ab4416f473",
      title: "Compra de produto",
      amount: 1000,
      quantity: 1,
      tangible: true,
      externalRef: "1234567890",
      transactionId: "1234567890",
      createdAt: "2025-07-01T14:27:28.477Z",
      updatedAt: "2025-07-01T14:27:28.477Z"
  },
  {
    id: "116c9339-1866-43be-a0d6-f03affb0d2e6",
    title: "Compra de produto",
    amount: 600,
    quantity: 1,
    tangible: true,
    externalRef: "1234567890",
    transactionId: "1234567890",
    createdAt: "2025-07-01T14:27:28.477Z",
    updatedAt: "2025-07-01T14:27:28.477Z"
  }
]