import { ItemTransaction, Transaction } from "./src/types/transaction";
import { Withdrawal } from "./src/types/withdrawal";

export const dataWithdrawal: Withdrawal[] = [
  {
      id: "cdb73696-dee1-4e48-a21f-1b700c2faf6a",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-08T17:54:51.145Z",
      processedAt: "2025-07-08T17:55:13.676Z",
      createdAt: "2025-07-01T17:54:47.243Z",
      externalRef: "R14mKX9PR5jkYVoq"
  },
  {
      id: "031c00ff-0f81-4d41-a270-0341ea08fbd8",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T16:10:31.509Z",
      processedAt: "2025-07-01T16:10:44.178Z",
      createdAt: "2025-07-01T16:10:27.884Z",
      externalRef: "5WzOwAWQJ1X19a43"
  },
  {
      id: "116c9149-1866-43be-a0d6-f03affb0d2e6",
      amount: 600,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "FAILED",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T16:06:09.695Z",
      processedAt: "2025-07-01T16:06:07.743Z",
      createdAt: "2025-07-01T16:06:07.743Z",
      externalRef: ""
  },
  {
      id: "b5a930ac-f277-4379-b12c-d1ab4416f473",
      amount: 1000,
      method: "PIX",
      pixKey: "38211277000136",
      pixKeyType: "CNPJ",
      status: "PAID",
      withdrawalType: "AUTOMATIC",
      approvedAt: "2025-07-01T14:27:41.082Z",
      processedAt: "2025-07-01T14:28:12.308Z",
      createdAt: "2025-07-01T14:27:28.477Z",
      externalRef: "JDQwLAdYO3jlRWoG"
  },
  {
    id: "116c9339-1866-43be-a0d6-f03affb0d2e6",
    amount: 600,
    method: "PIX",
    pixKey: "38211277000136",
    pixKeyType: "CNPJ",
    status: "FAILED",
    withdrawalType: "AUTOMATIC",
    approvedAt: "2025-07-01T14:27:41.082Z",
    processedAt: "2025-07-01T14:28:12.308Z",
    createdAt: "2025-07-01T14:27:28.477Z",
    externalRef: "JDQwLAdYO3jlRWoG"
  }
]



export const dataTransaction: ItemTransaction[] = [
  {
    id: "1",
    title: "Compra de teclado",
    amount: 100,
    quantity: 1,
    tangible: true,
    externalRef: "1234567890",
    transactionId: "TXN-10001",
    createdAt: "2025-07-01T10:12:45.000Z",
    updatedAt: "2025-07-01T10:12:45.000Z"
  },
  {
    id: "2",
    title: "Compra de mouse",
    amount: 50,
    quantity: 2,
    tangible: true,
    externalRef: "1234567891",
    transactionId: "TXN-10002",
    createdAt: "2025-07-01T15:30:00.000Z",
    updatedAt: "2025-07-01T15:30:00.000Z"
  },
  {
    id: "3",
    title: "Assinatura mensal",
    amount: 29.9,
    quantity: 1,
    tangible: false,
    externalRef: "1234567892",
    transactionId: "TXN-10003",
    createdAt: "2025-07-02T08:00:00.000Z",
    updatedAt: "2025-07-02T08:00:00.000Z"
  },
  {
    id: "4",
    title: "Compra de fone de ouvido",
    amount: 200,
    quantity: 1,
    tangible: true,
    externalRef: "1234567893",
    transactionId: "TXN-10004",
    createdAt: "2025-07-03T12:45:00.000Z",
    updatedAt: "2025-07-03T12:45:00.000Z"
  },
  {
    id: "5",
    title: "Compra de webcam",
    amount: 300,
    quantity: 1,
    tangible: true,
    externalRef: "1234567894",
    transactionId: "TXN-10005",
    createdAt: "2025-07-03T18:00:00.000Z",
    updatedAt: "2025-07-03T18:00:00.000Z"
  },
  {
    id: "6",
    title: "Licença de software",
    amount: 120.5,
    quantity: 1,
    tangible: false,
    externalRef: "1234567895",
    transactionId: "TXN-10006",
    createdAt: "2025-07-04T09:30:00.000Z",
    updatedAt: "2025-07-04T09:30:00.000Z"
  },
  {
    id: "7",
    title: "Compra de produto",
    amount: 60,
    quantity: 3,
    tangible: true,
    externalRef: "1234567896",
    transactionId: "TXN-10007",
    createdAt: "2025-07-05T14:20:00.000Z",
    updatedAt: "2025-07-05T14:20:00.000Z"
  },
  {
    id: "8",
    title: "Compra de produto",
    amount: 150,
    quantity: 1,
    tangible: true,
    externalRef: "1234567897",
    transactionId: "TXN-10008",
    createdAt: "2025-07-05T16:45:00.000Z",
    updatedAt: "2025-07-05T16:45:00.000Z"
  },
  {
    id: "9",
    title: "Assinatura premium",
    amount: 59.9,
    quantity: 1,
    tangible: false,
    externalRef: "1234567898",
    transactionId: "TXN-10009",
    createdAt: "2025-07-06T11:00:00.000Z",
    updatedAt: "2025-07-06T11:00:00.000Z"
  },
  {
    id: "10",
    title: "Compra de notebook",
    amount: 4500,
    quantity: 1,
    tangible: true,
    externalRef: "1234567899",
    transactionId: "TXN-10010",
    createdAt: "2025-07-07T10:00:00.000Z",
    updatedAt: "2025-07-07T10:00:00.000Z"
  },
  {
    id: "11",
    title: "Compra de mochila",
    amount: 120,
    quantity: 1,
    tangible: true,
    externalRef: "1234567810",
    transactionId: "TXN-10011",
    createdAt: "2025-07-07T12:00:00.000Z",
    updatedAt: "2025-07-07T12:00:00.000Z"
  },
  {
    id: "12",
    title: "Compra de curso online",
    amount: 199.9,
    quantity: 1,
    tangible: false,
    externalRef: "1234567811",
    transactionId: "TXN-10012",
    createdAt: "2025-07-08T08:30:00.000Z",
    updatedAt: "2025-07-08T08:30:00.000Z"
  }
];
